# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js 
# testing
coverage

# next.js
.next/
out/
next-env.d.ts

# production
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo

# turbo
.turbo

# ide
.idea/
.vscode/
.zed

# contentlayer
.contentlayer/

#aider
.aider.tags.cache.v4
.aider.input.history
.aider.chat.history.md
apps/zero-server/replica.db
apps/zero-server/replica.db-shm
apps/zero-server/replica.db-wal
apps/zero-server/replica.db-wal2
