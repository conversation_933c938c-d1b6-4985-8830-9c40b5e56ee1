"use client";

import React, { useCallback, useEffect, useState } from "react";

import { useParams } from "next/navigation";

import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { Loader2 } from "lucide-react";

import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { Button } from "@kit/ui/button";
import { Label } from "@kit/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@kit/ui/tabs";
import { Textarea } from "@kit/ui/textarea";

import { SelectedDocument } from "~/components/document-selector";
import { useZero } from "~/hooks/use-zero";

import { useEditorContent } from "../../context/ContentStudioContext";
import { AdvancedOptions } from "./AdvancedOptions";

// Interface for server-side content generation parameters
interface StudioContentGenerationParams {
  taskDescription: string;
  selectedCompanyContent: any;
  selectedPersonas: string[];
  personas: any[];
  selectedIcps: string[];
  icps: any[];
  selectedResearch: string[];
  researchItems: any[];
  selectedDocuments: SelectedDocument[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyBrand: any;
}

export const TextContentEditor: React.FC = () => {
  // Get data from context
  const params = useParams();
  const contentId = params.id;
  const { editor } = useEditorContent();
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [companyContent] = useZeroQuery(
    zero.query.company_content.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const selectedCompanyContent = companyContent.filter(
    (content: any) => content.id === contentId,
  )[0];

  const [savedResearch] = useZeroQuery(
    zero.query.saved_research.where('account_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [companyBrand] = useZeroQuery(
    zero.query.company_brand.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [personas] = useZeroQuery(
    zero.query.personas.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [icps] = useZeroQuery(
    zero.query.icps.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  // Add company_campaigns query
  const [companyCampaigns] = useZeroQuery(
    zero.query.company_campaigns.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  // Add product_documents query
  const [productDocuments] = useZeroQuery(
    zero.query.product_documents.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  // Local state

  // const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDescription, setTaskDescription] = useState('');

  // Advanced options state
  const [trendKeywords, setTrendKeywords] = useState<string[]>([]);
  const [seoKeywords, setSeoKeywords] = useState<string[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<SelectedDocument[]>([]);
  const [selectedIcps, setSelectedIcps] = useState<string[]>([]);
  const [icpItems, setIcpItems] = useState<any[]>(icps || []);
  const [selectedPersonas, setSelectedPersonas] = useState<any[]>([]);
  const [personaItems, setPersonaItems] = useState<any[]>(personas || []);
  const [selectedResearch, setSelectedResearch] = useState<string[]>([]);
  const [researchItems, setResearchItems] = useState<any[]>(savedResearch || []);
  // Initialize task title and description from company content
  // Use refs to track if user is actively editing to prevent state conflicts
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (selectedCompanyContent && !isInitialized) {
      setTaskTitle(selectedCompanyContent?.task_title || '');
      setTaskDescription(selectedCompanyContent?.task_description || '');
      setIsInitialized(true);
    }
  }, [selectedCompanyContent, isInitialized]);

  // Pre-populate advanced options from campaign data
  useEffect(() => {
    if (selectedCompanyContent?.campaign_id && companyCampaigns?.length > 0) {
      const associatedCampaign = companyCampaigns.find(
        (campaign: any) => campaign.id === selectedCompanyContent.campaign_id,
      );
      if (associatedCampaign) {
        // Pre-populate target_icps
        if (
          associatedCampaign.target_icps &&
          Array.isArray(associatedCampaign.target_icps)
        ) {
          setSelectedIcps(associatedCampaign.target_icps);
        }

        // Pre-populate target_personas
        if (
          associatedCampaign.target_personas &&
          Array.isArray(associatedCampaign.target_personas)
        ) {
          setSelectedPersonas(associatedCampaign.target_personas);
        }

        // Pre-populate external_research
        if (
          associatedCampaign.external_research &&
          Array.isArray(associatedCampaign.external_research)
        ) {
          setSelectedResearch(associatedCampaign.external_research);
        }

        // Pre-populate documents - convert product IDs to SelectedDocument objects
        if (
          associatedCampaign.products &&
          Array.isArray(associatedCampaign.products) &&
          productDocuments?.length > 0
        ) {
          const selectedDocs: SelectedDocument[] = associatedCampaign.products
            .map((productId: string) => {
              const doc = productDocuments.find((pd: any) => pd.id === productId);
              return doc
                ? {
                    id: doc.id,
                    documentTitle: doc.title,
                    content: doc.content || '',
                  }
                : null;
            })
            .filter(Boolean) as SelectedDocument[];

          setSelectedDocuments(selectedDocs);
        }
      }
    }
  }, [selectedCompanyContent, companyCampaigns, productDocuments]);

  // Create a memoized function to generate content using hybrid approach:
  // Server-side Langfuse prompt + BlockNote AI extension for editor integration
  const generateContent = useCallback(async () => {
    setIsGenerating(true);
    setHasError(false);

    if (!selectedCompanyContent?.id || !editor) {
      setIsGenerating(false);
      return;
    }

    try {
      // Prepare parameters for server-side prompt generation
      const params: StudioContentGenerationParams = {
        taskDescription,
        selectedCompanyContent,
        selectedPersonas,
        personas,
        selectedIcps,
        icps,
        selectedResearch,
        researchItems,
        selectedDocuments,
        seoKeywords,
        trendKeywords,
        companyBrand,
      };

      console.log('Fetching server-side generated prompt with params:', params);

      // Get the compiled prompt from the server (using Langfuse)
      const response = await fetch('/api/ai/generate-content-body', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to get prompt from server');
      }

      const result = await response.json();
      console.log('Server response:', result);

      // Use the server-generated content directly with BlockNote
      if (result.content) {
        const blocks = await editor.tryParseMarkdownToBlocks(result.content);

        // Replace the current editor content with the generated blocks
        const existingBlocks = editor.document;
        if (existingBlocks.length > 0) {
          editor.replaceBlocks(existingBlocks, blocks);
        } else {
          editor.insertBlocks(blocks, editor.getTextCursorPosition().block, 'after');
        }
      }

      setIsGenerating(false);
    } catch (error) {
      console.error('Error generating content:', error);
      setHasError(true);
      setIsGenerating(false);
    }
  }, [
    selectedCompanyContent,
    editor,
    taskDescription,
    selectedPersonas,
    personas,
    selectedIcps,
    icps,
    selectedResearch,
    researchItems,
    selectedDocuments,
    seoKeywords,
    trendKeywords,
    companyBrand,
  ]);

  const onDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTaskDescription(e.target.value);
    // Update Zero Sync Engine with the new task description
    // @ts-expect-error - TypeScript issue with Zero mutator typing
    zero.mutate.company_content.update({
      id: selectedCompanyContent?.id || '',
      values: {
        task_description: e.target.value,
      },
    });
  };

  return (
    <div className="space-y-4 p-4">
      {/* <div className="space-y-2">
        <Label className="text-lg font-semibold">Task Title</Label>
        <Textarea 
          value={taskTitle} 
          onChange={(e) => setTaskTitle(e.target.value)}
          className="text-lg"
          rows={2}
          disabled={isGenerating}
        />
      </div> */}

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <div className="space-y-2">
            <Label className="text-lg font-semibold">Topic</Label>
            <br />
            <Label className="text-muted-foreground text-sm">
              Enter the topic or basis used to generate the content.
            </Label>

            <Textarea
              value={taskDescription}
              onChange={onDescriptionChange}
              className="text-muted-foreground"
              rows={4}
              disabled={isGenerating}
            />
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <div className="space-y-2">
            <Label className="text-lg font-semibold">Topic</Label>
            <br />
            <Label className="text-muted-foreground text-sm">
              Enter the topic or basis used to generate the content.
            </Label>
            <Textarea
              value={taskDescription}
              onChange={onDescriptionChange}
              className="text-muted-foreground"
              rows={4}
              disabled={isGenerating}
            />
          </div>
          <AdvancedOptions
            selectedDocuments={selectedDocuments}
            onDocumentsChange={setSelectedDocuments}
            selectedIcps={selectedIcps}
            onIcpsChange={setSelectedIcps}
            icps={icpItems}
            onIcpsListChange={setIcpItems}
            selectedPersonas={selectedPersonas}
            onPersonasChange={setSelectedPersonas}
            personas={personaItems}
            onPersonasListChange={setPersonaItems}
            trendKeywords={trendKeywords}
            onTrendKeywordsChange={setTrendKeywords}
            seoKeywords={seoKeywords}
            onSeoKeywordsChange={setSeoKeywords}
            selectedResearch={selectedResearch}
            onResearchChange={setSelectedResearch}
            researchItems={researchItems}
            onResearchItemsChange={setResearchItems}
          />
        </TabsContent>
      </Tabs>

      <Button
        onClick={generateContent}
        disabled={isGenerating || !taskTitle.trim() || !taskDescription.trim()}
      >
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating...
          </>
        ) : (
          'Generate'
        )}
      </Button>

      {hasError && (
        <div className="mt-2">
          <button
            onClick={generateContent}
            className="cursor-pointer text-sm text-red-600 underline hover:text-red-800"
          >
            Error Generating, Click to try again
          </button>
        </div>
      )}

      {/* {generatedContent && (
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="keywords">
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <Label className="text-lg font-semibold">Keywords</Label>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm">SEO Keywords</Label>
                      <div className="mt-2 flex flex-wrap gap-2">
                        {generatedContent.seo_keywords_used.map((keyword, index) => (
                          <Badge key={index} variant="secondary">{keyword}</Badge>
                        ))}
                      </div>
                    </div>
                    {generatedContent.trend_keywords_used.length > 0 && (
                      <div>
                        <Separator className="my-4" />
                        <Label className="text-sm">Trend Keywords</Label>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {generatedContent.trend_keywords_used.map((keyword, index) => (
                            <Badge key={index} variant="outline">{keyword}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="rationale">
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <Label className="text-lg font-semibold">Creative Rationale</Label>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <p>{generatedContent.rationale_for_creative_choices}</p>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

          </div>
        </ScrollArea>
      )} */}
    </div>
  );
};
